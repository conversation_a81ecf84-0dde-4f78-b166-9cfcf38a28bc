-- 最后冲刺，达到80%覆盖度

-- 1. 中转站3到中转站5的连接（双向）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
UNION ALL
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2;

SELECT '中转站3<->5连接完成' as status, COUNT(*) as total_records FROM travel_time;

-- 2. 中转站3到中转站6的连接（双向）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
UNION ALL
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2;

SELECT '中转站3<->6连接完成' as status, COUNT(*) as total_records FROM travel_time;

-- 3. 中转站4到中转站5的连接（双向）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
UNION ALL
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2;

SELECT '中转站4<->5连接完成' as status, COUNT(*) as total_records FROM travel_time;

-- 4. 中转站4到中转站6的连接（双向）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
UNION ALL
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2;

SELECT '中转站4<->6连接完成' as status, COUNT(*) as total_records FROM travel_time;

-- 5. 中转站5到中转站6的连接（双向）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
UNION ALL
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2;

SELECT '中转站5<->6连接完成' as status, COUNT(*) as total_records FROM travel_time;

-- 6. 最终统计
SELECT 
    '最终覆盖度冲刺完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 3331650.0 * 100, 2) as coverage_percentage
FROM travel_time;
