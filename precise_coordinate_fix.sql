-- 精确修复travel_time表，确保坐标与accumulation和transit_depot表完全匹配

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 创建临时表存储所有有效的坐标点
DROP TEMPORARY TABLE IF EXISTS temp_coordinates;
CREATE TEMPORARY TABLE temp_coordinates (
    longitude_str VARCHAR(20),
    latitude_str VARCHAR(20),
    longitude_num DECIMAL(10,6),
    latitude_num DECIMAL(10,6),
    point_type VARCHAR(20),
    point_id BIGINT,
    transit_depot_id BIGINT
);

-- 3. 插入所有聚集区坐标（使用FORMAT确保6位小数）
INSERT INTO temp_coordinates (longitude_str, latitude_str, longitude_num, latitude_num, point_type, point_id, transit_depot_id)
SELECT 
    FORMAT(longitude, 6) as longitude_str,
    FORMAT(latitude, 6) as latitude_str,
    longitude as longitude_num,
    latitude as latitude_num,
    'accumulation' as point_type,
    accumulation_id as point_id,
    transit_depot_id
FROM accumulation 
WHERE is_delete = 0 AND longitude != 0 AND latitude != 0;

-- 4. 插入所有中转站坐标
INSERT INTO temp_coordinates (longitude_str, latitude_str, longitude_num, latitude_num, point_type, point_id, transit_depot_id)
SELECT 
    FORMAT(CAST(longitude AS DECIMAL(10,6)), 6) as longitude_str,
    FORMAT(CAST(latitude AS DECIMAL(10,6)), 6) as latitude_str,
    CAST(longitude AS DECIMAL(10,6)) as longitude_num,
    CAST(latitude AS DECIMAL(10,6)) as latitude_num,
    'transit_depot' as point_type,
    transit_depot_id as point_id,
    transit_depot_id
FROM transit_depot 
WHERE is_delete = 0;

-- 5. 为每个中转站生成内部的点对点时间数据
-- 中转站1
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 1 
  AND c2.transit_depot_id = 1
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 中转站2
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 2 
  AND c2.transit_depot_id = 2
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 中转站3
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 3 
  AND c2.transit_depot_id = 3
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 中转站4
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 4 
  AND c2.transit_depot_id = 4
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 中转站5
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 5 
  AND c2.transit_depot_id = 5
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 中转站6
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    c1.longitude_str as longitude_start,
    c1.latitude_str as latitude_start,
    c2.longitude_str as longitude_end,
    c2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(c2.latitude_num) - RADIANS(c1.latitude_num)) / 2), 2) +
            COS(RADIANS(c1.latitude_num)) * COS(RADIANS(c2.latitude_num)) * 
            POWER(SIN((RADIANS(c2.longitude_num) - RADIANS(c1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_coordinates c1
CROSS JOIN temp_coordinates c2
WHERE c1.transit_depot_id = 6 
  AND c2.transit_depot_id = 6
  AND NOT (c1.longitude_str = c2.longitude_str AND c1.latitude_str = c2.latitude_str);

-- 6. 清理临时表
DROP TEMPORARY TABLE temp_coordinates;

-- 7. 显示结果统计
SELECT 
    '精确坐标修复完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 678460.0 * 100, 2) as coverage_percentage
FROM travel_time;

-- 8. 验证坐标匹配
SELECT 
    '坐标匹配验证' as check_type,
    COUNT(*) as matched_count
FROM travel_time tt
WHERE EXISTS (
    SELECT 1 FROM accumulation a 
    WHERE FORMAT(a.longitude, 6) = tt.longitude_start 
    AND FORMAT(a.latitude, 6) = tt.latitude_start
    AND a.is_delete = 0
);
