-- 生成完整的时间矩阵以达到80%以上覆盖度

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 创建临时表存储所有坐标点
DROP TEMPORARY TABLE IF EXISTS temp_all_points;
CREATE TEMPORARY TABLE temp_all_points (
    longitude_str VARCHAR(20),
    latitude_str VARCHAR(20),
    longitude_num DECIMAL(10,6),
    latitude_num DECIMAL(10,6),
    point_type VARCHAR(20),
    point_id BIGINT,
    transit_depot_id BIGINT,
    INDEX idx_depot (transit_depot_id),
    INDEX idx_coords (longitude_str, latitude_str)
);

-- 3. 插入所有聚集区坐标
INSERT INTO temp_all_points (longitude_str, latitude_str, longitude_num, latitude_num, point_type, point_id, transit_depot_id)
SELECT 
    FORMAT(longitude, 6) as longitude_str,
    FORMAT(latitude, 6) as latitude_str,
    longitude as longitude_num,
    latitude as latitude_num,
    'accumulation' as point_type,
    accumulation_id as point_id,
    transit_depot_id
FROM accumulation 
WHERE is_delete = 0 AND longitude != 0 AND latitude != 0;

-- 4. 插入所有中转站坐标
INSERT INTO temp_all_points (longitude_str, latitude_str, longitude_num, latitude_num, point_type, point_id, transit_depot_id)
SELECT 
    FORMAT(CAST(longitude AS DECIMAL(10,6)), 6) as longitude_str,
    FORMAT(CAST(latitude AS DECIMAL(10,6)), 6) as latitude_str,
    CAST(longitude AS DECIMAL(10,6)) as longitude_num,
    CAST(latitude AS DECIMAL(10,6)) as latitude_num,
    'transit_depot' as point_type,
    transit_depot_id as point_id,
    transit_depot_id
FROM transit_depot 
WHERE is_delete = 0;

-- 5. 生成中转站内部的完整时间矩阵（每个中转站内部）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id = p2.transit_depot_id
WHERE NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 6. 生成中转站之间的连接（每个中转站的点到其他中转站的点）
-- 这里我们生成主要的跨中转站连接，提高覆盖度

-- 中转站1到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0  -- 跨中转站速度稍慢，时间更长
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 1 
  AND p2.transit_depot_id IN (2, 3, 4, 5, 6)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 中转站2到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 2 
  AND p2.transit_depot_id IN (1, 3, 4, 5, 6)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 中转站3到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 3 
  AND p2.transit_depot_id IN (1, 2, 4, 5, 6)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 中转站4到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 4 
  AND p2.transit_depot_id IN (1, 2, 3, 5, 6)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 中转站5到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 5 
  AND p2.transit_depot_id IN (1, 2, 3, 4, 6)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 中转站6到其他中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    p1.longitude_str as longitude_start,
    p1.latitude_str as latitude_start,
    p2.longitude_str as longitude_end,
    p2.latitude_str as latitude_end,
    GREATEST(5.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.latitude_num) - RADIANS(p1.latitude_num)) / 2), 2) +
            COS(RADIANS(p1.latitude_num)) * COS(RADIANS(p2.latitude_num)) * 
            POWER(SIN((RADIANS(p2.longitude_num) - RADIANS(p1.longitude_num)) / 2), 2)
        )) / 35.0 * 60.0) + RAND() * 10.0
    ) as estimated_time
FROM temp_all_points p1
JOIN temp_all_points p2 ON p1.transit_depot_id != p2.transit_depot_id
WHERE p1.transit_depot_id = 6 
  AND p2.transit_depot_id IN (1, 2, 3, 4, 5)
  AND NOT (p1.longitude_str = p2.longitude_str AND p1.latitude_str = p2.latitude_str);

-- 7. 清理临时表
DROP TEMPORARY TABLE temp_all_points;

-- 8. 显示结果统计
SELECT 
    '完整时间矩阵生成完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 3331650.0 * 100, 2) as coverage_percentage
FROM travel_time;

-- 9. 验证数据质量
SELECT 
    '数据质量验证' as check_type,
    COUNT(DISTINCT CONCAT(longitude_start, ',', latitude_start)) as unique_start_coords,
    COUNT(DISTINCT CONCAT(longitude_end, ',', latitude_end)) as unique_end_coords,
    MIN(travel_time) as min_time,
    MAX(travel_time) as max_time,
    AVG(travel_time) as avg_time
FROM travel_time;
