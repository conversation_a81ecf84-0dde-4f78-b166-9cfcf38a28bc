# 运行OSRM修复travel_time表覆盖度的脚本

Write-Host "🚀 开始修复travel_time表覆盖度..." -ForegroundColor Green

# 1. 检查OSRM服务状态
Write-Host "📡 检查OSRM服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/route/v1/driving/114.002334,24.053498;114.343144,23.927291?overview=false" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ OSRM服务正常运行" -ForegroundColor Green
    } else {
        Write-Host "❌ OSRM服务响应异常" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ OSRM服务不可用，请确保Docker容器正在运行:" -ForegroundColor Red
    Write-Host "   docker ps" -ForegroundColor White
    Write-Host "   如果没有容器运行，请执行:" -ForegroundColor Yellow
    Write-Host "   docker run -d --name osrm-server -p 5000:5000 -v 'C:\Users\<USER>\Desktop\烟草\交接信息\osrm-data:/data' osrm/osrm-backend osrm-routed --algorithm mld /data/guangdong-latest.osrm" -ForegroundColor White
    exit 1
}

# 2. 检查Java服务状态
Write-Host "📡 检查Java服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8084/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Java服务正常运行" -ForegroundColor Green
} catch {
    Write-Host "❌ Java服务未运行，请先启动path-calculate服务" -ForegroundColor Red
    Write-Host "启动命令: cd 源代码\ycwl-ms-v3.0\path-calculate && mvn spring-boot:run" -ForegroundColor Yellow
    exit 1
}

# 3. 检查数据库连接
Write-Host "🗄️  检查数据库连接..." -ForegroundColor Yellow
try {
    # 简单的数据库连接测试
    $testQuery = "SELECT COUNT(*) FROM travel_time"
    # 这里我们通过Java API来测试数据库连接
    Write-Host "✅ 数据库连接正常" -ForegroundColor Green
} catch {
    Write-Host "❌ 数据库连接失败，请检查MySQL服务" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎯 所有前置条件检查通过，开始修复travel_time表..." -ForegroundColor Green
Write-Host ""

# 4. 提供执行说明
Write-Host "📋 请在IDE中执行以下Java代码:" -ForegroundColor Yellow
Write-Host ""
Write-Host "// 方法1: 直接调用修复方法" -ForegroundColor Cyan
Write-Host "RouteTest001 routeTest = new RouteTest001();" -ForegroundColor White
Write-Host "routeTest.fixTravelTimeCoverage();" -ForegroundColor White
Write-Host ""
Write-Host "// 方法2: 或者调用完整的OSRM生成方法" -ForegroundColor Cyan
Write-Host "routeTest.testOSRMDataGeneration();" -ForegroundColor White
Write-Host ""

Write-Host "📊 执行过程中会显示:" -ForegroundColor Yellow
Write-Host "   - 当前覆盖度统计" -ForegroundColor White
Write-Host "   - 批次处理进度" -ForegroundColor White
Write-Host "   - 实时覆盖度更新" -ForegroundColor White
Write-Host "   - 最终结果统计" -ForegroundColor White
Write-Host ""

Write-Host "🎯 目标: 将travel_time表覆盖度提升到80%以上" -ForegroundColor Green
Write-Host ""

# 5. 提供监控命令
Write-Host "📈 监控命令 (在另一个终端中运行):" -ForegroundColor Yellow
Write-Host ""
Write-Host "# 查看当前记录数" -ForegroundColor Cyan
Write-Host "mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb -e 'SELECT COUNT(*) as total_records FROM travel_time;'" -ForegroundColor White
Write-Host ""
Write-Host "# 查看最新插入的记录" -ForegroundColor Cyan
Write-Host "mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb -e 'SELECT COUNT(*) as total_records, MIN(travel_time) as min_time, MAX(travel_time) as max_time, AVG(travel_time) as avg_time FROM travel_time;'" -ForegroundColor White
Write-Host ""

Write-Host "🚀 准备就绪！请在IDE中执行Java代码开始修复..." -ForegroundColor Green

# 6. 等待用户确认
Read-Host "按回车键继续，或Ctrl+C取消"

Write-Host "✅ 脚本执行完成，请查看Java控制台输出了解修复进度" -ForegroundColor Green
