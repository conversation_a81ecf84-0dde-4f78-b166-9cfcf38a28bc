# 简化的OSRM安装脚本

Write-Host "🚀 开始安装OSRM..." -ForegroundColor Green

# 创建工作目录
$workDir = "osrm-setup"
if (!(Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir
    Write-Host "📁 创建目录: $workDir" -ForegroundColor Yellow
}

Set-Location $workDir

# 下载OSRM Windows版本
Write-Host "📥 下载OSRM Windows版本..." -ForegroundColor Yellow
$osrmUrl = "https://github.com/Project-OSRM/osrm-backend/releases/download/v5.27.1/osrm-backend-v5.27.1-win64.zip"
$osrmZip = "osrm-win64.zip"

if (!(Test-Path $osrmZip)) {
    try {
        Write-Host "正在下载OSRM (约50MB)..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $osrmUrl -OutFile $osrmZip -UseBasicParsing
        Write-Host "✅ OSRM下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载: $osrmUrl" -ForegroundColor Yellow
        Write-Host "保存为: $PWD\$osrmZip" -ForegroundColor Yellow
        Read-Host "下载完成后按回车继续"
    }
}

# 解压OSRM
if (Test-Path $osrmZip) {
    Write-Host "📦 解压OSRM..." -ForegroundColor Yellow
    try {
        Expand-Archive -Path $osrmZip -DestinationPath "." -Force
        Write-Host "✅ OSRM解压完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 解压失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 下载地图数据
Write-Host "🗺️  下载地图数据..." -ForegroundColor Yellow
$mapUrl = "http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
$mapFile = "guangdong.osm.pbf"

if (!(Test-Path $mapFile)) {
    Write-Host "正在下载广东省地图 (约200MB)..." -ForegroundColor Cyan
    Write-Host "这需要几分钟..." -ForegroundColor Yellow
    
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($mapUrl, "$PWD\$mapFile")
        Write-Host "✅ 地图下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载: $mapUrl" -ForegroundColor Yellow
        Write-Host "保存为: $PWD\$mapFile" -ForegroundColor Yellow
        Read-Host "下载完成后按回车继续"
    }
}

# 下载配置文件
Write-Host "⚙️  下载配置文件..." -ForegroundColor Yellow
$carLuaUrl = "https://raw.githubusercontent.com/Project-OSRM/osrm-backend/master/profiles/car.lua"

if (!(Test-Path "car.lua")) {
    try {
        Invoke-WebRequest -Uri $carLuaUrl -OutFile "car.lua" -UseBasicParsing
        Write-Host "✅ 配置文件下载完成" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  配置文件下载失败，创建简化版本" -ForegroundColor Yellow
        
        # 创建简化的car.lua
        Write-Host "创建简化配置文件..." -ForegroundColor Cyan

        # 使用Here-String创建Lua文件
        @'
api_version = 4

function setup()
  return {
    properties = {
      max_speed_for_map_matching = 180/3.6,
      use_turn_restrictions = true,
      continue_straight_at_waypoint = true,
      weight_name = 'routability',
    },
    default_mode = 1,
    default_speed = 50,
    oneway_handling = true,
    turn_penalty = 7.5,
    speeds = {
      motorway = 120,
      motorway_link = 80,
      trunk = 100,
      trunk_link = 70,
      primary = 80,
      primary_link = 60,
      secondary = 60,
      secondary_link = 50,
      tertiary = 50,
      tertiary_link = 40,
      unclassified = 40,
      residential = 30,
      living_street = 20,
      service = 20
    }
  }
end

function process_node(profile, node, result)
  local barrier = node:get_value_by_key("barrier")
  if barrier then
    result.barrier = true
  end

  local tag = node:get_value_by_key("highway")
  if "traffic_signals" == tag then
    result.traffic_lights = true
  end
end

function process_way(profile, way, result)
  local data = {
    highway = way:get_value_by_key('highway'),
  }

  if (not data.highway or data.highway == '') then
    return
  end

  if data.highway == 'construction' then
    return
  end

  result.forward_mode = 1
  result.backward_mode = 1

  local highway_speed = profile.default_speed
  if profile.speeds[data.highway] then
    highway_speed = profile.speeds[data.highway]
  end

  result.forward_speed = highway_speed
  result.backward_speed = highway_speed

  local name = way:get_value_by_key('name')
  if name and "" ~= name then
    result.name = name
  else
    result.name = ""
  end

  local oneway = way:get_value_by_key('oneway')
  if oneway == "-1" then
    result.forward_mode = 0
  elseif oneway == "yes" or oneway == "1" or oneway == "true" then
    result.backward_mode = 0
  end

  result.weight = result.distance / result.forward_speed
end

function process_turn(profile, turn)
  if turn.has_traffic_light then
    turn.weight = turn.weight + 2
  end

  if turn.angle >= 0 then
    turn.weight = turn.weight + profile.turn_penalty / (1 + math.exp(-((13) * turn.angle/180 - 6.5)))
  else
    turn.weight = turn.weight + profile.turn_penalty / (1 + math.exp(-((13) * -turn.angle/180 - 6.5)))
  end
end

return {
  setup = setup,
  process_way = process_way,
  process_node = process_node,
  process_turn = process_turn
}
'@ | Out-File -FilePath "car.lua" -Encoding UTF8
        Write-Host "✅ 创建简化配置文件" -ForegroundColor Green
    }
}

Set-Location ..

Write-Host "`n🎉 OSRM安装完成！" -ForegroundColor Green
Write-Host "📁 安装目录: osrm-setup" -ForegroundColor Cyan

Write-Host "`n📝 下一步:" -ForegroundColor Yellow
Write-Host "1. 运行: .\process-osrm-data.ps1" -ForegroundColor White
Write-Host "2. 运行: .\start-osrm.ps1" -ForegroundColor White
