-- 最终修复：确保travel_time表与accumulation表坐标完全匹配

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 生成完全匹配的时间矩阵数据
-- 使用当前accumulation和transit_depot表的实际坐标

-- 为每个中转站生成内部时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    -- 中转站1的所有点（聚集区+中转站）
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 1 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 1 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 中转站2
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 2 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 2 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 中转站3
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 3 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 3 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 中转站4
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 4 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 4 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 中转站5
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 5 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 5 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 中转站6
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(p1.lng, 6) as longitude_start,
    FORMAT(p1.lat, 6) as latitude_start,
    FORMAT(p2.lng, 6) as longitude_end,
    FORMAT(p2.lat, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(p2.lat) - RADIANS(p1.lat)) / 2), 2) +
            COS(RADIANS(p1.lat)) * COS(RADIANS(p2.lat)) * 
            POWER(SIN((RADIANS(p2.lng) - RADIANS(p1.lng)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 6 AND is_delete = 0
) p1
CROSS JOIN (
    SELECT longitude as lng, latitude as lat, transit_depot_id 
    FROM accumulation 
    WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)) as lng, CAST(latitude AS DECIMAL(10,6)) as lat, transit_depot_id 
    FROM transit_depot 
    WHERE transit_depot_id = 6 AND is_delete = 0
) p2
WHERE NOT (p1.lng = p2.lng AND p1.lat = p2.lat);

-- 显示结果
SELECT 
    '最终修复完成' as status,
    COUNT(*) as total_records
FROM travel_time;

-- 验证匹配度
SELECT 
    '匹配验证' as check_type,
    COUNT(DISTINCT tt.longitude_start) as unique_start_coords,
    (SELECT COUNT(DISTINCT FORMAT(longitude, 6)) FROM accumulation WHERE is_delete = 0 AND longitude != 0) as total_acc_coords
FROM travel_time tt;
