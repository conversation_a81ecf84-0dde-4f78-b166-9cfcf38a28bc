# Windows OSRM完整安装脚本

Write-Host "🚀 开始安装OSRM for Windows..." -ForegroundColor Green

# 创建工作目录
$workDir = "osrm-setup"
if (!(Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir
    Write-Host "📁 创建工作目录: $workDir" -ForegroundColor Yellow
}

Set-Location $workDir

# 第1步：下载预编译的OSRM Windows版本
Write-Host "`n📥 第1步：下载OSRM Windows版本..." -ForegroundColor Cyan

$osrmUrl = "https://github.com/Project-OSRM/osrm-backend/releases/download/v5.27.1/osrm-backend-v5.27.1-win64.zip"
$osrmZip = "osrm-backend-win64.zip"

if (!(Test-Path $osrmZip)) {
    Write-Host "正在下载OSRM (约50MB)..." -ForegroundColor Yellow
    try {
        # 使用PowerShell下载
        Invoke-WebRequest -Uri $osrmUrl -OutFile $osrmZip -UseBasicParsing
        Write-Host "✅ OSRM下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败，尝试备用方案..." -ForegroundColor Red
        
        # 备用：使用curl
        try {
            curl -L -o $osrmZip $osrmUrl
            Write-Host "✅ OSRM下载完成 (curl)" -ForegroundColor Green
        } catch {
            Write-Host "❌ 所有下载方式都失败，请手动下载:" -ForegroundColor Red
            Write-Host "URL: $osrmUrl" -ForegroundColor White
            Write-Host "保存为: $PWD\$osrmZip" -ForegroundColor White
            Read-Host "下载完成后按回车继续"
        }
    }
} else {
    Write-Host "✅ OSRM文件已存在" -ForegroundColor Green
}

# 解压OSRM
if (!(Test-Path "osrm-backend")) {
    Write-Host "📦 解压OSRM..." -ForegroundColor Yellow
    try {
        Expand-Archive -Path $osrmZip -DestinationPath "." -Force
        Write-Host "✅ OSRM解压完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 解压失败，请手动解压 $osrmZip" -ForegroundColor Red
        Read-Host "解压完成后按回车继续"
    }
} else {
    Write-Host "✅ OSRM已解压" -ForegroundColor Green
}

# 第2步：下载韶关市地图数据
Write-Host "`n🗺️  第2步：下载韶关市地图数据..." -ForegroundColor Cyan

# 由于韶关市数据较小，我们直接下载广东省数据的一个小区域
$mapUrl = "http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
$mapFile = "guangdong-latest.osm.pbf"

if (!(Test-Path $mapFile)) {
    Write-Host "正在下载广东省地图数据 (约200MB)..." -ForegroundColor Yellow
    Write-Host "这可能需要几分钟时间..." -ForegroundColor Yellow
    
    try {
        # 显示下载进度
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($mapUrl, "$PWD\$mapFile")
        Write-Host "✅ 地图数据下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败，尝试curl..." -ForegroundColor Red
        try {
            curl -L -o $mapFile $mapUrl
            Write-Host "✅ 地图数据下载完成 (curl)" -ForegroundColor Green
        } catch {
            Write-Host "❌ 下载失败，请手动下载:" -ForegroundColor Red
            Write-Host "URL: $mapUrl" -ForegroundColor White
            Write-Host "保存为: $PWD\$mapFile" -ForegroundColor White
            Read-Host "下载完成后按回车继续"
        }
    }
} else {
    Write-Host "✅ 地图数据已存在" -ForegroundColor Green
}

# 第3步：下载car.lua配置文件
Write-Host "`n⚙️  第3步：下载配置文件..." -ForegroundColor Cyan

$carLuaUrl = "https://raw.githubusercontent.com/Project-OSRM/osrm-backend/master/profiles/car.lua"
if (!(Test-Path "car.lua")) {
    Write-Host "下载car.lua配置文件..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $carLuaUrl -OutFile "car.lua" -UseBasicParsing
        Write-Host "✅ 配置文件下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败，使用默认配置" -ForegroundColor Yellow
        # 创建简化的car.lua
        $defaultCarLua = @"
api_version = 4

Set = require('lib/set')
Sequence = require('lib/sequence')
Handlers = require("lib/way_handlers")
find_access_tag = require("lib/access").find_access_tag

function setup()
  return {
    properties = {
      max_speed_for_map_matching      = 180/3.6, -- 180kmph -> m/s
      use_turn_restrictions           = true,
      continue_straight_at_waypoint   = true,
      weight_name                     = 'routability',
    },
    
    default_mode              = mode.driving,
    default_speed             = 50,
    oneway_handling           = true,
    side_road_multiplier      = 0.8,
    turn_penalty              = 7.5,
    speed_reduction           = 0.8,

    -- classify highway tags when necessary for turn weights
    highway_turn_classification = {
    },

    -- classify access tags when necessary for turn weights
    access_turn_classification = {
    }
  }
end

function process_node(profile, node, result)
  -- parse access and barrier tags
  local access = find_access_tag(node, profile.access_tags_hierarchy)
  if access then
    if profile.access_tag_blacklist[access] then
      result.barrier = true
    end
  end
  local barrier = node:get_value_by_key("barrier")
  if barrier then
    --  make an exception for rising bollard barriers
    local bollard = node:get_value_by_key("bollard")
    local rising_bollard = bollard and "rising" == bollard

    if not profile.barrier_whitelist[barrier] and not rising_bollard then
      result.barrier = true
    end
  end

  -- check if node is a traffic light
  local tag = node:get_value_by_key("highway")
  if "traffic_signals" == tag then
    result.traffic_lights = true
  end
end

function process_way(profile, way, result)
  -- the intial filtering of ways based on presence of tags
  -- affects processing times significantly, because all ways
  -- have to be checked.
  -- to increase performance, prefetching and intial tag check
  -- is done in directly instead of via a handler.

  -- in general we should  try to abort as soon as
  -- possible if the way is not routable, to avoid doing
  -- unnecessary work. this implies we should check things that
  -- commonly forbid access early, and handle edge cases later.

  -- data table for storing intermediate values during processing
  local data = {
    -- prefetch tags
    highway = way:get_value_by_key('highway'),
  }

  -- perform an quick initial check and abort if the way is
  -- obviously not routable.
  -- highway or route tags must be present for a way to be routable
  if (not data.highway or data.highway == '') then
    return
  end

  -- don't route on ways that are still under construction
  if data.highway == 'construction' then
    return
  end

  -- access
  local access = find_access_tag(way, profile.access_tags_hierarchy)
  if access and profile.access_tag_blacklist[access] then
    return
  end

  result.forward_mode = mode.driving
  result.backward_mode = mode.driving

  -- speed
  local bridge = way:get_value_by_key('bridge')
  local tunnel = way:get_value_by_key('tunnel')
  local maxspeed = parse_maxspeed(way:get_value_by_key ( 'maxspeed') )
  local maxspeed_forward = parse_maxspeed(way:get_value_by_key( 'maxspeed:forward'))
  local maxspeed_backward = parse_maxspeed(way:get_value_by_key( 'maxspeed:backward'))

  -- Set the avg speed on the way if it is accessible by road class
  local highway_speed = profile.default_speed
  local max_speed = math.huge
  if profile.speeds[data.highway] then
    highway_speed = profile.speeds[data.highway]
    max_speed = highway_speed
  end

  -- limit speed to highway-specific maxspeeds
  if maxspeed and maxspeed > 0 then
    max_speed = math.min(max_speed, maxspeed)
  end

  -- Set forward and backward speed
  result.forward_speed = max_speed
  result.backward_speed = max_speed

  if maxspeed_forward and maxspeed_forward > 0 then
    result.forward_speed = math.min(result.forward_speed, maxspeed_forward)
  end

  if maxspeed_backward and maxspeed_backward > 0 then
    result.backward_speed = math.min(result.backward_speed, maxspeed_backward)
  end

  -- reduce speed on bad surfaces
  local surface = way:get_value_by_key('surface')
  local tracktype = way:get_value_by_key('tracktype')
  local smoothness = way:get_value_by_key('smoothness')

  if surface and profile.surface_speeds[surface] then
    result.forward_speed = math.min(profile.surface_speeds[surface], result.forward_speed)
    result.backward_speed = math.min(profile.surface_speeds[surface], result.backward_speed)
  end
  if tracktype and profile.tracktype_speeds[tracktype] then
    result.forward_speed = math.min(profile.tracktype_speeds[tracktype], result.forward_speed)
    result.backward_speed = math.min(profile.tracktype_speeds[tracktype], result.backward_speed)
  end
  if smoothness and profile.smoothness_speeds[smoothness] then
    result.forward_speed = math.min(profile.smoothness_speeds[smoothness], result.forward_speed)
    result.backward_speed = math.min(profile.smoothness_speeds[smoothness], result.backward_speed)
  end

  -- parse the remaining tags
  local name = way:get_value_by_key('name')
  local ref = way:get_value_by_key('ref')
  local junction = way:get_value_by_key('junction')
  local route = way:get_value_by_key('route')
  local man_made = way:get_value_by_key('man_made')
  local barrier = way:get_value_by_key('barrier')
  local oneway = way:get_value_by_key('oneway')
  local oneway_bicycle = way:get_value_by_key('oneway:bicycle')
  local cycleway = way:get_value_by_key('cycleway')
  local cycleway_left = way:get_value_by_key('cycleway:left')
  local cycleway_right = way:get_value_by_key('cycleway:right')
  local duration = way:get_value_by_key('duration')
  local service = way:get_value_by_key('service')
  local foot = way:get_value_by_key('foot')
  local foot_forward = way:get_value_by_key('foot:forward')
  local foot_backward = way:get_value_by_key('foot:backward')
  local bicycle = way:get_value_by_key('bicycle')

  -- name
  if ref and "" ~= ref and name and "" ~= name then
    result.name = name .. " (" .. ref .. ")"
  elseif ref and "" ~= ref then
    result.name = ref
  elseif name and "" ~= name then
    result.name = name
  else
    result.name = ""
  end

  -- roundabout handling
  if "roundabout" == junction then
    result.roundabout = true;
  end

  -- ferry handling
  if "ferry" == route then
    result.forward_mode = mode.ferry
    result.backward_mode = mode.ferry
    result.ignore_in_grid = true
    if duration and durationIsValid(duration) then
      result.duration = math.max( parseDuration(duration), 1 )
    else
      result.forward_speed = profile.ferry_speed
      result.backward_speed = profile.ferry_speed
    end
  end

  -- movable bridge
  if bridge and "movable" == bridge then
    result.forward_speed = profile.bridge_speed
    result.backward_speed = profile.bridge_speed
  end

  -- tunnels
  if tunnel and profile.tunnel_speeds[tunnel] then
    result.forward_speed = math.min(profile.tunnel_speeds[tunnel], result.forward_speed)
    result.backward_speed = math.min(profile.tunnel_speeds[tunnel], result.backward_speed)
  end

  -- bridges
  if bridge and profile.bridge_speeds[bridge] then
    result.forward_speed = math.min(profile.bridge_speeds[bridge], result.forward_speed)
    result.backward_speed = math.min(profile.bridge_speeds[bridge], result.backward_speed)
  end

  -- leave early of this way is not accessible
  if result.forward_speed <= 0 and result.backward_speed <= 0 then
    return
  end

  -- oneway handling
  if oneway_bicycle == "yes" or oneway_bicycle == "1" or oneway_bicycle == "true" then
    result.backward_mode = mode.inaccessible
  elseif oneway_bicycle == "no" or oneway_bicycle == "0" or oneway_bicycle == "false" then
     -- prevent other oneway handling
  elseif oneway_bicycle == "-1" then
    result.forward_mode = mode.inaccessible
  elseif oneway == "-1" then
    result.forward_mode = mode.inaccessible
  elseif oneway == "yes" or oneway == "1" or oneway == "true" then
    result.backward_mode = mode.inaccessible
  end

  -- cycleway handling
  Handlers.cycleway(profile,way,result,data)

  -- bike handling
  Handlers.bicycle(profile,way,result)

  -- foot handling
  Handlers.foot(profile,way,result)

  -- maxweight and height restriction handling
  Handlers.restrictions(profile,way,result)

  -- service handling
  Handlers.service(profile,way,result)

  -- man made handling
  Handlers.man_made(profile,way,result)

  -- access handling
  Handlers.access(profile,way,result)

  -- handle turn lanes and road classification for turn weight computation
  Handlers.classification(profile,way,result,data)

  -- handle hov lanes
  Handlers.hov(profile,way,result,data)

  -- handle destination tags
  Handlers.destinations(profile,way,result)

  -- set weight properties of the way
  result.weight = result.distance / result.forward_speed
end

function process_turn (profile, turn)
  -- Use a sigmoid function to return a penalty that maxes out at turn_penalty
  -- over the space of 0-180 degrees.  Values here were chosen by fitting
  -- the function to some turn penalty samples from real driving.
  local turn_penalty = profile.turn_penalty
  local turn_bias = turn.is_left_hand_driving and 1. / profile.turn_bias or profile.turn_bias

  if turn.has_traffic_light then
      turn.weight = turn.weight + profile.traffic_light_penalty
  end

  if turn.number_of_roads > 2 or turn.source_mode ~= turn.target_mode or turn.is_u_turn then
    if turn.angle >= 0 then
      turn.weight = turn.weight + turn_penalty / (1 + math.exp( -((13 / turn_bias) *  turn.angle/180 - 6.5*turn_bias)))
    else
      turn.weight = turn.weight + turn_penalty / (1 + math.exp( -((13 * turn_bias) * -turn.angle/180 - 6.5/turn_bias)))
    end

    if turn.is_u_turn then
      turn.weight = turn.weight + turn_penalty
    end
  end
end

return {
  setup = setup,
  process_way = process_way,
  process_node = process_node,
  process_turn = process_turn
}
"@
        $defaultCarLua | Out-File -FilePath "car.lua" -Encoding UTF8
        Write-Host "✅ 创建默认配置文件" -ForegroundColor Green
    }
} else {
    Write-Host "✅ 配置文件已存在" -ForegroundColor Green
}

Write-Host "`n🎉 OSRM安装完成！" -ForegroundColor Green
Write-Host "📁 安装目录: $PWD" -ForegroundColor Cyan
Write-Host "🗺️  地图文件: $mapFile" -ForegroundColor Cyan

# 返回上级目录
Set-Location ..

Write-Host "`n📝 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 运行: .\process-osrm-data.ps1" -ForegroundColor White
Write-Host "2. 运行: .\start-osrm-service.ps1" -ForegroundColor White
Write-Host "3. 测试: .\test-osrm-service.ps1" -ForegroundColor White
