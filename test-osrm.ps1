# Test OSRM service

Write-Host "Testing OSRM service..." -ForegroundColor Green

Start-Sleep 5

try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/route/v1/driving/114.002334,24.053498;114.343144,23.927291?overview=false" -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "OSRM service is working!" -ForegroundColor Green
        
        $jsonResponse = $response.Content | ConvertFrom-Json
        if ($jsonResponse.code -eq "Ok") {
            $route = $jsonResponse.routes[0]
            $distance = $route.distance
            $duration = $route.duration
            
            Write-Host "Test route results:" -ForegroundColor Cyan
            Write-Host "  Distance: $([math]::Round($distance/1000, 2)) km" -ForegroundColor White
            Write-Host "  Duration: $([math]::Round($duration/60, 1)) minutes" -ForegroundColor White
        } else {
            Write-Host "OSRM returned error: $($jsonResponse.code)" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "OSRM service not ready yet: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test table service
Write-Host "Testing table service..." -ForegroundColor Yellow
try {
    $coordinates = "114.002334,24.053498;114.343144,23.927291;114.19539,24.05113"
    $tableUrl = "http://localhost:5000/table/v1/driving/$coordinates"
    
    $response = Invoke-WebRequest -Uri $tableUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "Table service working!" -ForegroundColor Green
        $jsonResponse = $response.Content | ConvertFrom-Json
        if ($jsonResponse.code -eq "Ok") {
            Write-Host "Distance matrix (3x3) generated successfully" -ForegroundColor Cyan
            
            $durations = $jsonResponse.durations
            $distances = $jsonResponse.distances
            
            Write-Host "Duration matrix (minutes):" -ForegroundColor Cyan
            for ($i = 0; $i -lt 3; $i++) {
                $row = ""
                for ($j = 0; $j -lt 3; $j++) {
                    $time = [math]::Round($durations[$i][$j]/60, 1)
                    $row += "$time".PadLeft(6) + " "
                }
                Write-Host "  $row" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "Table service test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "OSRM service test completed!" -ForegroundColor Green
