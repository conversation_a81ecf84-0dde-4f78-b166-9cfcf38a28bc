#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import sys

def test_time_matrix_coverage():
    """测试时间矩阵覆盖度计算"""
    
    # 数据库连接
    connection = pymysql.connect(
        host='localhost',
        port=3307,
        user='root',
        password='aA13717028793#',
        database='ycdb',
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 1. 获取所有聚集区和中转站坐标
            cursor.execute("""
                SELECT longitude, latitude, transit_depot_id, 'accumulation' as type
                FROM accumulation 
                WHERE is_delete = 0 AND longitude IS NOT NULL AND latitude IS NOT NULL
                UNION ALL
                SELECT longitude, latitude, transit_depot_id, 'depot' as type
                FROM transit_depot 
                WHERE is_delete = 0
                ORDER BY transit_depot_id
            """)
            
            all_points = cursor.fetchall()
            print(f"总坐标点数: {len(all_points)}")
            
            # 2. 按中转站分组
            depot_points = {}
            for point in all_points:
                lng, lat, depot_id, point_type = point
                if depot_id not in depot_points:
                    depot_points[depot_id] = []
                depot_points[depot_id].append((lng, lat, point_type))
            
            # 3. 计算每个中转站内部的期望点对数
            total_expected_pairs = 0
            for depot_id, points in depot_points.items():
                point_count = len(points)
                expected_pairs = point_count * (point_count - 1)  # 不包括自环
                total_expected_pairs += expected_pairs
                print(f"中转站{depot_id}: {point_count}个点, 期望{expected_pairs}个点对")
            
            print(f"总期望点对数: {total_expected_pairs}")
            
            # 4. 查询travel_time表中的实际记录数
            cursor.execute("SELECT COUNT(*) FROM travel_time")
            actual_pairs = cursor.fetchone()[0]
            print(f"实际点对数: {actual_pairs}")
            
            # 5. 计算覆盖度
            coverage = actual_pairs / total_expected_pairs if total_expected_pairs > 0 else 0
            print(f"覆盖度: {coverage:.2%}")
            
            # 6. 测试Python脚本的IN查询逻辑
            print("\n测试IN查询匹配:")
            for depot_id in [1, 2, 3]:  # 测试前3个中转站
                # 获取该中转站的所有坐标
                cursor.execute("""
                    SELECT CAST(longitude AS CHAR), CAST(latitude AS CHAR)
                    FROM accumulation 
                    WHERE transit_depot_id = %s AND is_delete = 0
                    UNION
                    SELECT CAST(longitude AS CHAR), CAST(latitude AS CHAR)
                    FROM transit_depot 
                    WHERE transit_depot_id = %s
                """, (depot_id, depot_id))
                
                depot_coords = cursor.fetchall()
                lng_values = [coord[0] for coord in depot_coords]
                lat_values = [coord[1] for coord in depot_coords]
                
                # 构建IN查询
                lng_placeholders = ','.join(['%s'] * len(lng_values))
                lat_placeholders = ','.join(['%s'] * len(lat_values))
                
                query = f"""
                    SELECT COUNT(*) 
                    FROM travel_time
                    WHERE longitude_start IN ({lng_placeholders})
                        AND latitude_start IN ({lat_placeholders})
                        AND longitude_end IN ({lng_placeholders})
                        AND latitude_end IN ({lat_placeholders})
                """
                
                cursor.execute(query, lng_values + lat_values + lng_values + lat_values)
                matched_records = cursor.fetchone()[0]
                
                expected_for_depot = len(depot_coords) * (len(depot_coords) - 1)
                depot_coverage = matched_records / expected_for_depot if expected_for_depot > 0 else 0
                
                print(f"中转站{depot_id}: {len(depot_coords)}个坐标点, 匹配{matched_records}条记录, 覆盖度{depot_coverage:.2%}")
            
    finally:
        connection.close()

if __name__ == "__main__":
    test_time_matrix_coverage()
