-- 快速修复travel_time表覆盖度的SQL脚本
-- 使用距离估算方法快速生成缺失的travel_time数据

-- 创建临时表存储所有需要的点对点组合
DROP TEMPORARY TABLE IF EXISTS temp_point_pairs;
CREATE TEMPORARY TABLE temp_point_pairs (
    longitude_start VARCHAR(20),
    latitude_start VARCHAR(20), 
    longitude_end VARCHAR(20),
    latitude_end VARCHAR(20),
    transit_depot_id BIGINT,
    estimated_time DOUBLE
);

-- 为每个中转站生成内部点对点的时间数据
-- 中转站1 (118个聚集区)
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end, 
    CAST(a2.latitude AS CHAR) as latitude_end,
    1 as transit_depot_id,
    -- 使用Haversine公式估算时间（分钟）
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0  -- 40km/h平均速度，加随机因子
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0  
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站2 (237个聚集区)
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    2 as transit_depot_id,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站3 (168个聚集区)
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    3 as transit_depot_id,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站4 (328个聚集区)
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    4 as transit_depot_id,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站5 (497个聚集区)
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    5 as transit_depot_id,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站6 (473个聚集区)  
INSERT INTO temp_point_pairs (longitude_start, latitude_start, longitude_end, latitude_end, transit_depot_id, estimated_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    6 as transit_depot_id,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 插入到travel_time表（只插入不存在的记录）
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    longitude_start,
    latitude_start, 
    longitude_end,
    latitude_end,
    estimated_time
FROM temp_point_pairs;

-- 清理临时表
DROP TEMPORARY TABLE temp_point_pairs;

-- 显示结果统计
SELECT 
    '修复完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 678460.0 * 100, 2) as coverage_percentage
FROM travel_time;
