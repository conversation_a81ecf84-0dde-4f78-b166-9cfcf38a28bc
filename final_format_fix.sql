-- 最终修复：确保中转站和聚集区坐标格式完全一致

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 重新生成数据，中转站坐标也使用CAST(longitude AS CHAR)格式

-- 中转站1内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站1最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站2内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站2最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站3内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站3最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站4内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站4最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站5内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站5最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站6内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT longitude, latitude FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站6最终修复完成' as status, COUNT(*) as records FROM travel_time;

-- 最终统计
SELECT 
    '最终修复完成' as status,
    COUNT(*) as total_records
FROM travel_time;

-- 验证中转站坐标是否包含在内
SELECT 
    '中转站坐标验证' as check_type,
    d.transit_depot_id,
    CAST(d.longitude AS CHAR) as depot_lng,
    CAST(d.latitude AS CHAR) as depot_lat,
    COUNT(t.longitude_start) as found_in_travel_time
FROM transit_depot d
LEFT JOIN travel_time t ON CAST(d.longitude AS CHAR) = t.longitude_start AND CAST(d.latitude AS CHAR) = t.latitude_start
WHERE d.is_delete = 0
GROUP BY d.transit_depot_id, d.longitude, d.latitude;
