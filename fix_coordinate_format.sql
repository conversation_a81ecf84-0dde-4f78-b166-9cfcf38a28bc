-- 修复travel_time表坐标格式，确保与算法期望的6位小数格式匹配

-- 1. 首先清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 重新生成数据，确保坐标格式完全匹配算法期望
-- 使用FORMAT函数确保6位小数精度

-- 中转站1的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站2的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站3的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站4的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站5的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 中转站6的数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1
CROSS JOIN (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 显示结果统计
SELECT 
    '坐标格式修复完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 678460.0 * 100, 2) as coverage_percentage
FROM travel_time;

-- 验证坐标格式
SELECT 
    '坐标格式验证' as check_type,
    longitude_start, 
    latitude_start,
    LENGTH(longitude_start) as lng_length,
    LENGTH(latitude_start) as lat_length
FROM travel_time 
LIMIT 5;
