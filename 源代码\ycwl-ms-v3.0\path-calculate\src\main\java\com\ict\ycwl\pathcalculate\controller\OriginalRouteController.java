package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.pathcalculate.DirectRouteRunner;
import com.ict.ycwl.pathcalculate.RouteTest001;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**

 * <AUTHOR> @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/original-route")
@ConditionalOnProperty(name = "legacy.travel-time.enabled", havingValue = "true", matchIfMissing = false)
public class OriginalRouteController {
    
    @Autowired
    private DirectRouteRunner directRouteRunner;

    @Autowired
    private RouteTest001 routeTest001;

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**

     */
    @PostMapping("/test06")
    public Map<String, Object> runTest06() {


        Map<String, Object> response = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();


            directRouteRunner.runTest06();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            response.put("success", true);
            response.put("message", "1");
            response.put("duration", duration);
            response.put("durationMinutes", duration / 1000.0 / 60.0);

            log.info("test06方法执行完成，耗时: {}分钟", duration / 1000.0 / 60.0);

        } catch (Exception e) {
            log.error("test06方法执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }

        return response;
    }
    
    /**
     * 直接调用前辈的test07方法（修复无效记录）
     */
    @PostMapping("/test07")
    public Map<String, Object> runTest07() {
        log.info("开始执行test07方法...");

        Map<String, Object> response = new HashMap<>();

        try {
            long startTime = System.currentTimeMillis();

            // 使用DirectRouteRunner调用前辈的test07方法
            directRouteRunner.runTest07();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            response.put("success", true);
            response.put("message", "前辈的test07方法执行完成");
            response.put("duration", duration);
            response.put("durationMinutes", duration / 1000.0 / 60.0);

            log.info("前辈的test07方法执行完成，耗时: {}分钟", duration / 1000.0 / 60.0);

        } catch (Exception e) {
            log.error("前辈的test07方法执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }

        return response;
    }
    
    /**
     * 按前辈的顺序执行：先test06，再test07
     */
    @PostMapping("/run-all")
    public Map<String, Object> runAll() {
        log.info("开始按前辈的顺序执行：test06 -> test07");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long totalStartTime = System.currentTimeMillis();
            
            // 使用DirectRouteRunner执行完整流程
            directRouteRunner.runAll();
            
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;

            response.put("success", true);
            response.put("message", "前辈的完整流程执行完成");
            response.put("totalDuration", totalDuration);
            response.put("totalDurationMinutes", totalDuration / 1000.0 / 60.0);

            log.info("前辈的完整流程执行完成，总耗时: {}分钟", totalDuration / 1000.0 / 60.0);
            
        } catch (Exception e) {
            log.error("前辈的完整流程执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 清理并重新生成travel_time表
     * 专门用于提高覆盖度到80%以上
     */
    @GetMapping("/clean-and-regenerate")
    public Map<String, Object> cleanAndRegenerateTravelTime() {
        log.info("开始清理并重新生成travel_time表...");

        Map<String, Object> response = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            // 1. 删除无效记录（起点或终点为0,0的记录）
            String deleteSql = "DELETE FROM travel_time WHERE " +
                    "longitude_start = 0.0 OR latitude_start = 0.0 OR " +
                    "longitude_end = 0.0 OR latitude_end = 0.0 OR " +
                    "travel_time <= 0 OR travel_time IS NULL";
            int deletedCount = jdbcTemplate.update(deleteSql);
            log.info("删除了 {} 条无效记录", deletedCount);

            // 2. 统计当前有效记录数
            String countSql = "SELECT COUNT(*) FROM travel_time";
            int currentCount = jdbcTemplate.queryForObject(countSql, Integer.class);
            log.info("当前有效记录数: {}", currentCount);

            // 3. 统计应该有的总记录数（有效坐标点的组合）
            String validPointsSql = "SELECT COUNT(*) FROM accumulation WHERE " +
                    "longitude != 0.0 AND latitude != 0.0 AND is_delete = 0";
            int validPoints = jdbcTemplate.queryForObject(validPointsSql, Integer.class);
            int expectedTotal = validPoints * (validPoints - 1); // n*(n-1)
            log.info("预期总记录数: {} (基于 {} 个有效坐标点)", expectedTotal, validPoints);

            // 4. 计算覆盖度
            double coverage = (double) currentCount / expectedTotal * 100;
            log.info("当前覆盖度: {:.2f}%", coverage);

            if (coverage >= 80.0) {
                response.put("success", true);
                response.put("message", "覆盖度已达到80%以上，无需重新生成");
                response.put("coverage", coverage);
                response.put("currentCount", currentCount);
                response.put("expectedTotal", expectedTotal);
                return response;
            }

            // 5. 如果覆盖度不足，调用test06重新生成
            log.info("覆盖度不足80%，开始重新生成数据...");
            directRouteRunner.runTest06();

            // 6. 重新统计
            int finalCount = jdbcTemplate.queryForObject(countSql, Integer.class);
            double finalCoverage = (double) finalCount / expectedTotal * 100;

            long duration = System.currentTimeMillis() - startTime;

            response.put("success", true);
            response.put("message", "travel_time表清理和重新生成完成");
            response.put("deletedInvalidRecords", deletedCount);
            response.put("initialCount", currentCount);
            response.put("finalCount", finalCount);
            response.put("expectedTotal", expectedTotal);
            response.put("initialCoverage", coverage);
            response.put("finalCoverage", finalCoverage);
            response.put("durationMinutes", duration / 60000.0);

            log.info("完成！最终覆盖度: {:.2f}%", finalCoverage);

        } catch (Exception e) {
            log.error("清理和重新生成失败", e);
            response.put("success", false);
            response.put("message", "清理和重新生成失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取前辈方法的说明
     */
    @GetMapping("/info")
    public Map<String, Object> getInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("description", "直接调用前辈RouteTest001的原始方法");
        response.put("test06", "前辈的主要生成方法，现已修复为使用高德API");
        response.put("test07", "前辈的修复方法，使用高德API修复无效记录");
        response.put("runAll", "按前辈的顺序执行完整流程：test06 -> test07");
        response.put("cleanAndRegenerate", "清理无效数据并重新生成，确保覆盖度达到80%以上");
        response.put("note", "已修复OSRM依赖问题，现在完全使用高德API");
        return response;
    }

    /**
     * 按算法要求生成travel_time数据（中转站内部全连接）
     */
    @GetMapping("/generate-algorithm-travel-time")
    public Map<String, Object> generateAlgorithmTravelTime() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("开始按算法要求生成travel_time数据...");

            routeTest001.generateTravelTimeForAlgorithm();

            response.put("success", true);
            response.put("message", "按算法要求生成travel_time数据完成");

        } catch (Exception e) {
            log.error("生成travel_time数据失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 检查算法覆盖度
     */
    @GetMapping("/check-algorithm-coverage")
    public Map<String, Object> checkAlgorithmCoverage() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("检查算法覆盖度...");

            routeTest001.checkAlgorithmCoverage();

            response.put("success", true);
            response.put("message", "覆盖度检查完成");

        } catch (Exception e) {
            log.error("检查覆盖度失败", e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
        }

        return response;
    }
}
