# 改进travel_time表数据质量的脚本

Write-Host "🚀 开始改进travel_time表数据质量..." -ForegroundColor Green

# 检查Java服务是否运行
Write-Host "📡 检查Java服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8084/health" -UseBasicParsing -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Java服务运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Java服务未运行，请先启动path-calculate服务" -ForegroundColor Red
    Write-Host "启动命令: cd 源代码\ycwl-ms-v3.0\path-calculate && mvn spring-boot:run" -ForegroundColor Yellow
    exit 1
}

# 检查数据库连接
Write-Host "🗄️  检查数据库连接..." -ForegroundColor Yellow
try {
    $dbTest = mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb -e "SELECT COUNT(*) as current_records FROM travel_time;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 数据库连接正常" -ForegroundColor Green
        Write-Host "当前travel_time记录数: $($dbTest -split '\n' | Select-Object -Last 2 | Select-Object -First 1)" -ForegroundColor Cyan
    } else {
        throw "数据库连接失败"
    }
} catch {
    Write-Host "❌ 数据库连接失败，请检查MySQL服务" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎯 选择数据改进方案:" -ForegroundColor Yellow
Write-Host "1. 使用高德API生成高质量数据 (推荐)" -ForegroundColor White
Write-Host "2. 使用改进的距离估算算法" -ForegroundColor White
Write-Host "3. 清理并重新生成当前数据" -ForegroundColor White

$choice = Read-Host "请选择方案 (1-3)"

switch ($choice) {
    "1" {
        Write-Host "`n🗺️  使用高德API生成高质量数据..." -ForegroundColor Green
        
        # 调用Java方法生成高质量数据
        Write-Host "📞 调用Java服务生成数据..." -ForegroundColor Yellow
        
        # 创建临时Java调用脚本
        $javaScript = @"
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import com.ict.ycwl.pathcalculate.PathCalculateApplication;
import com.ict.ycwl.pathcalculate.RouteTest001;

public class TravelTimeImprover {
    public static void main(String[] args) {
        System.out.println("🚀 启动travel_time数据改进程序...");
        
        ConfigurableApplicationContext context = SpringApplication.run(PathCalculateApplication.class, args);
        RouteTest001 routeTest = context.getBean(RouteTest001.class);
        
        try {
            // 使用改进的算法生成数据
            routeTest.generateTravelTimeForAlgorithm();
            System.out.println("✅ 数据生成完成");
        } catch (Exception e) {
            System.out.println("❌ 数据生成失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            context.close();
        }
    }
}
"@
        
        Write-Host "由于需要Spring上下文，请手动在IDE中运行以下代码:" -ForegroundColor Yellow
        Write-Host "RouteTest001 routeTest = new RouteTest001();" -ForegroundColor White
        Write-Host "routeTest.generateTravelTimeForAlgorithm();" -ForegroundColor White
    }
    
    "2" {
        Write-Host "`n📐 使用改进的距离估算算法..." -ForegroundColor Green
        
        # 执行改进的SQL脚本
        Write-Host "🔄 执行改进算法..." -ForegroundColor Yellow
        
        $improvedSql = @"
-- 改进的travel_time数据生成算法

-- 1. 备份当前数据
CREATE TABLE IF NOT EXISTS travel_time_backup AS SELECT * FROM travel_time;

-- 2. 清空当前数据
TRUNCATE TABLE travel_time;

-- 3. 使用改进算法重新生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    -- 改进的时间计算算法
    CASE 
        -- 同一点，时间为0
        WHEN a1.longitude = a2.longitude AND a1.latitude = a2.latitude THEN 0
        -- 短距离（市内）：基于实际道路系数
        WHEN distance_km <= 5 THEN GREATEST(2.0, distance_km / 25.0 * 60.0 + RAND() * 3.0)
        -- 中距离（城际）：考虑道路类型
        WHEN distance_km <= 20 THEN GREATEST(5.0, distance_km / 35.0 * 60.0 + RAND() * 8.0)
        -- 长距离（可能有高速）：更快速度
        ELSE GREATEST(10.0, distance_km / 60.0 * 60.0 + RAND() * 15.0)
    END as estimated_time
FROM (
    SELECT 
        a1.*,
        a2.longitude as lng2, a2.latitude as lat2,
        -- 计算实际距离（公里）
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        ))) as distance_km
    FROM (
        SELECT longitude, latitude, transit_depot_id FROM accumulation 
        WHERE is_delete = 0 AND longitude != 0 AND latitude != 0
        UNION ALL
        SELECT longitude, latitude, transit_depot_id FROM transit_depot 
        WHERE is_delete = 0
    ) a1
    CROSS JOIN (
        SELECT longitude, latitude, transit_depot_id FROM accumulation 
        WHERE is_delete = 0 AND longitude != 0 AND latitude != 0
        UNION ALL
        SELECT longitude, latitude, transit_depot_id FROM transit_depot 
        WHERE is_delete = 0
    ) a2
    WHERE a1.transit_depot_id = a2.transit_depot_id  -- 只计算同一中转站内部
) distance_calc
WHERE distance_km > 0;  -- 排除同一点

-- 4. 统计结果
SELECT 
    '改进算法完成' as status,
    COUNT(*) as total_records,
    MIN(travel_time) as min_time,
    MAX(travel_time) as max_time,
    AVG(travel_time) as avg_time
FROM travel_time;
"@
        
        $improvedSql | Out-File -FilePath "improved_algorithm.sql" -Encoding UTF8
        
        Write-Host "执行改进算法..." -ForegroundColor Yellow
        mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb < improved_algorithm.sql
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 改进算法执行成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 改进算法执行失败" -ForegroundColor Red
        }
    }
    
    "3" {
        Write-Host "`n🧹 清理并重新生成当前数据..." -ForegroundColor Green
        
        Write-Host "执行数据清理..." -ForegroundColor Yellow
        mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb < final_format_fix.sql
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 数据清理完成" -ForegroundColor Green
        } else {
            Write-Host "❌ 数据清理失败" -ForegroundColor Red
        }
    }
    
    default {
        Write-Host "❌ 无效选择" -ForegroundColor Red
        exit 1
    }
}

# 验证最终结果
Write-Host "`n📊 验证最终结果..." -ForegroundColor Yellow
$finalStats = mysql -h localhost -P 3307 -u root -paA13717028793# -D ycdb -e "
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT longitude_start) as unique_starts,
    COUNT(DISTINCT longitude_end) as unique_ends,
    MIN(travel_time) as min_time,
    MAX(travel_time) as max_time,
    AVG(travel_time) as avg_time
FROM travel_time;
"

Write-Host "最终统计结果:" -ForegroundColor Cyan
Write-Host $finalStats -ForegroundColor White

Write-Host "`n🧪 测试算法..." -ForegroundColor Yellow
try {
    $testResponse = Invoke-WebRequest -Uri "http://localhost:8084/path/calculateAll?apiKey=3729e38b382749ba3a10bae7539e0d9a" -UseBasicParsing -TimeoutSec 300
    if ($testResponse.StatusCode -eq 200) {
        Write-Host "🎉 算法测试成功！" -ForegroundColor Green
        Write-Host $testResponse.Content -ForegroundColor White
    } else {
        Write-Host "⚠️  算法返回状态码: $($testResponse.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 算法测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n✅ travel_time表改进完成！" -ForegroundColor Green
