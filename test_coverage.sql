-- 测试时间矩阵覆盖度计算

-- 1. 统计每个中转站的坐标点数和期望点对数
SELECT 
    '中转站坐标点统计' as test_type,
    transit_depot_id,
    COUNT(*) as point_count,
    COUNT(*) * (COUNT(*) - 1) as expected_pairs
FROM (
    SELECT longitude, latitude, transit_depot_id
    FROM accumulation 
    WHERE is_delete = 0 AND longitude IS NOT NULL AND latitude IS NOT NULL
    UNION ALL
    SELECT longitude, latitude, transit_depot_id
    FROM transit_depot 
    WHERE is_delete = 0
) all_points
GROUP BY transit_depot_id
ORDER BY transit_depot_id;

-- 2. 计算总期望点对数
SELECT 
    '总期望点对数' as test_type,
    SUM(point_count * (point_count - 1)) as total_expected_pairs
FROM (
    SELECT 
        transit_depot_id,
        COUNT(*) as point_count
    FROM (
        SELECT longitude, latitude, transit_depot_id
        FROM accumulation 
        WHERE is_delete = 0 AND longitude IS NOT NULL AND latitude IS NOT NULL
        UNION ALL
        SELECT longitude, latitude, transit_depot_id
        FROM transit_depot 
        WHERE is_delete = 0
    ) all_points
    GROUP BY transit_depot_id
) depot_stats;

-- 3. 统计travel_time表中的实际记录数
SELECT 
    '实际点对数' as test_type,
    COUNT(*) as actual_pairs
FROM travel_time;

-- 4. 测试中转站1的IN查询匹配
SELECT 
    '中转站1 IN查询测试' as test_type,
    COUNT(*) as matched_records
FROM travel_time
WHERE longitude_start IN (
    SELECT CAST(longitude AS CHAR) FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0
    UNION
    SELECT CAST(longitude AS CHAR) FROM transit_depot WHERE transit_depot_id = 1
)
AND latitude_start IN (
    SELECT CAST(latitude AS CHAR) FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0
    UNION
    SELECT CAST(latitude AS CHAR) FROM transit_depot WHERE transit_depot_id = 1
)
AND longitude_end IN (
    SELECT CAST(longitude AS CHAR) FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0
    UNION
    SELECT CAST(longitude AS CHAR) FROM transit_depot WHERE transit_depot_id = 1
)
AND latitude_end IN (
    SELECT CAST(latitude AS CHAR) FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0
    UNION
    SELECT CAST(latitude AS CHAR) FROM transit_depot WHERE transit_depot_id = 1
);

-- 5. 验证坐标格式一致性
SELECT 
    '坐标格式一致性验证' as test_type,
    a.longitude as acc_lng,
    t.longitude_start as travel_lng,
    CASE WHEN a.longitude = t.longitude_start THEN 'MATCH' ELSE 'NO_MATCH' END as status
FROM accumulation a
JOIN travel_time t ON a.longitude = t.longitude_start AND a.latitude = t.latitude_start
WHERE a.transit_depot_id = 1 AND a.is_delete = 0
LIMIT 5;

-- 6. 计算覆盖度百分比
SELECT 
    '覆盖度计算' as test_type,
    actual_pairs,
    total_expected_pairs,
    ROUND(actual_pairs * 100.0 / total_expected_pairs, 2) as coverage_percentage
FROM (
    SELECT COUNT(*) as actual_pairs FROM travel_time
) actual,
(
    SELECT 
        SUM(point_count * (point_count - 1)) as total_expected_pairs
    FROM (
        SELECT 
            transit_depot_id,
            COUNT(*) as point_count
        FROM (
            SELECT longitude, latitude, transit_depot_id
            FROM accumulation 
            WHERE is_delete = 0 AND longitude IS NOT NULL AND latitude IS NOT NULL
            UNION ALL
            SELECT longitude, latitude, transit_depot_id
            FROM transit_depot 
            WHERE is_delete = 0
        ) all_points
        GROUP BY transit_depot_id
    ) depot_stats
) expected;
