# 韶关市OSRM地图数据设置脚本

Write-Host "🗺️  开始设置韶关市OSRM地图数据..." -ForegroundColor Green

# 创建数据目录
$dataDir = "osrm-data"
if (!(Test-Path $dataDir)) {
    New-Item -ItemType Directory -Path $dataDir
    Write-Host "📁 创建数据目录: $dataDir" -ForegroundColor Yellow
}

Set-Location $dataDir

# 韶关市边界框坐标 (大致范围)
# 经度: 113.0° - 115.0°
# 纬度: 24.0° - 26.0°
$bbox = "113.0,24.0,115.0,26.0"

Write-Host "📍 韶关市坐标范围: $bbox" -ForegroundColor Cyan

# 方案1: 下载广东省数据并提取韶关市区域
Write-Host "📥 下载广东省OSM数据..." -ForegroundColor Yellow

# 检查是否已有广东省数据
if (!(Test-Path "guangdong-latest.osm.pbf")) {
    try {
        # 使用curl下载（Windows 10+自带）
        Write-Host "正在下载广东省OSM数据 (约200MB)..." -ForegroundColor Yellow
        curl -L -o "guangdong-latest.osm.pbf" "http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
        Write-Host "✓ 广东省数据下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 下载失败，请手动下载:" -ForegroundColor Red
        Write-Host "URL: http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf" -ForegroundColor White
        Write-Host "保存到: $PWD\guangdong-latest.osm.pbf" -ForegroundColor White
        exit 1
    }
} else {
    Write-Host "✓ 广东省数据已存在" -ForegroundColor Green
}

# 方案2: 如果有osmosis，提取韶关市区域
Write-Host "🔧 检查osmosis工具..." -ForegroundColor Yellow
try {
    $osmosisVersion = osmosis --version 2>$null
    Write-Host "✓ 找到osmosis: $osmosisVersion" -ForegroundColor Green
    
    if (!(Test-Path "shaoguan.osm.pbf")) {
        Write-Host "✂️  提取韶关市区域数据..." -ForegroundColor Yellow
        osmosis --read-pbf "guangdong-latest.osm.pbf" --bounding-box top=26.0 left=113.0 bottom=24.0 right=115.0 --write-pbf "shaoguan.osm.pbf"
        Write-Host "✓ 韶关市区域数据提取完成" -ForegroundColor Green
    } else {
        Write-Host "✓ 韶关市数据已存在" -ForegroundColor Green
    }
    $mapFile = "shaoguan.osm.pbf"
} catch {
    Write-Host "⚠️  osmosis未安装，将使用完整广东省数据" -ForegroundColor Yellow
    $mapFile = "guangdong-latest.osm.pbf"
}

# 检查OSRM工具
Write-Host "🔧 检查OSRM工具..." -ForegroundColor Yellow
try {
    $osrmVersion = osrm-extract --version 2>$null
    Write-Host "✓ 找到OSRM: $osrmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ OSRM未安装！请先安装OSRM:" -ForegroundColor Red
    Write-Host "1. 下载OSRM Windows版本" -ForegroundColor White
    Write-Host "2. 或使用Docker: docker pull osrm/osrm-backend" -ForegroundColor White
    Write-Host "3. 或使用WSL安装Linux版本" -ForegroundColor White
    exit 1
}

# 预处理地图数据
Write-Host "⚙️  预处理地图数据..." -ForegroundColor Yellow

# 下载car.lua配置文件（如果不存在）
if (!(Test-Path "car.lua")) {
    Write-Host "📥 下载car.lua配置文件..." -ForegroundColor Yellow
    try {
        curl -L -o "car.lua" "https://raw.githubusercontent.com/Project-OSRM/osrm-backend/master/profiles/car.lua"
        Write-Host "✓ car.lua下载完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ car.lua下载失败，请手动下载" -ForegroundColor Red
        exit 1
    }
}

# 提取路网
if (!(Test-Path "$($mapFile -replace '\.osm\.pbf$', '.osrm')")) {
    Write-Host "🛣️  提取路网数据..." -ForegroundColor Yellow
    osrm-extract -p "car.lua" $mapFile
    Write-Host "✓ 路网提取完成" -ForegroundColor Green
} else {
    Write-Host "✓ 路网数据已存在" -ForegroundColor Green
}

$osrmFile = $mapFile -replace '\.osm\.pbf$', '.osrm'

# 分区
if (!(Test-Path "$($osrmFile).partition")) {
    Write-Host "📊 创建路网分区..." -ForegroundColor Yellow
    osrm-partition $osrmFile
    Write-Host "✓ 分区完成" -ForegroundColor Green
} else {
    Write-Host "✓ 分区数据已存在" -ForegroundColor Green
}

# 自定义
if (!(Test-Path "$($osrmFile).customize")) {
    Write-Host "🎯 自定义路网..." -ForegroundColor Yellow
    osrm-customize $osrmFile
    Write-Host "✓ 自定义完成" -ForegroundColor Green
} else {
    Write-Host "✓ 自定义数据已存在" -ForegroundColor Green
}

Write-Host "🎉 韶关市OSRM数据准备完成！" -ForegroundColor Green
Write-Host ""
Write-Host "现在可以启动OSRM服务:" -ForegroundColor Yellow
Write-Host "osrm-routed --algorithm mld $osrmFile" -ForegroundColor White
Write-Host ""
Write-Host "或者使用启动脚本:" -ForegroundColor Yellow
Write-Host ".\Start-OSRM-Shaoguan.ps1" -ForegroundColor White

# 创建韶关市专用启动脚本
$startScript = @"
# 韶关市OSRM服务启动脚本

Write-Host "🚀 启动韶关市OSRM服务..." -ForegroundColor Green

Set-Location osrm-data

# 检查数据文件
if (!(Test-Path "$osrmFile")) {
    Write-Host "❌ OSRM数据文件不存在: $osrmFile" -ForegroundColor Red
    Write-Host "请先运行: .\setup-shaoguan-osrm.ps1" -ForegroundColor Yellow
    exit 1
}

# 启动OSRM服务
Write-Host "🌐 启动OSRM服务在端口5000..." -ForegroundColor Yellow
Write-Host "地图数据: 韶关市 ($mapFile)" -ForegroundColor Cyan

try {
    osrm-routed --algorithm mld $osrmFile --port 5000
} catch {
    Write-Host "❌ OSRM服务启动失败" -ForegroundColor Red
    Write-Host "请检查OSRM是否正确安装" -ForegroundColor Yellow
    exit 1
}
"@

$startScript | Out-File -FilePath "..\Start-OSRM-Shaoguan.ps1" -Encoding UTF8
Write-Host "📝 创建启动脚本: Start-OSRM-Shaoguan.ps1" -ForegroundColor Green

Set-Location ..

Write-Host ""
Write-Host "🎯 下一步:" -ForegroundColor Yellow
Write-Host "1. 运行: .\Start-OSRM-Shaoguan.ps1" -ForegroundColor White
Write-Host "2. 等待OSRM服务启动完成" -ForegroundColor White
Write-Host "3. 运行RouteTest001方法填充travel_time表" -ForegroundColor White
