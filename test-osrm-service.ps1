# 测试OSRM服务是否正常工作

Write-Host "🧪 测试OSRM服务..." -ForegroundColor Green

$osrmUrl = "http://localhost:5000"

# 测试服务是否启动
Write-Host "📡 检查OSRM服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$osrmUrl/route/v1/driving/114.002334,24.053498;114.343144,23.927291?overview=false" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ OSRM服务运行正常" -ForegroundColor Green
        
        # 解析响应
        $jsonResponse = $response.Content | ConvertFrom-Json
        if ($jsonResponse.code -eq "Ok") {
            $route = $jsonResponse.routes[0]
            $distance = $route.distance
            $duration = $route.duration
            
            Write-Host "📊 测试路径结果:" -ForegroundColor Cyan
            Write-Host "   起点: 114.002334,24.053498 (韶关市区)" -ForegroundColor White
            Write-Host "   终点: 114.343144,23.927291 (韶关市区)" -ForegroundColor White
            Write-Host "   距离: $([math]::Round($distance/1000, 2)) 公里" -ForegroundColor White
            Write-Host "   时间: $([math]::Round($duration/60, 1)) 分钟" -ForegroundColor White
        } else {
            Write-Host "⚠️  OSRM返回错误: $($jsonResponse.code)" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ OSRM服务不可用: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保:" -ForegroundColor Yellow
    Write-Host "1. 已运行 .\setup-shaoguan-osrm.ps1" -ForegroundColor White
    Write-Host "2. 已启动 .\Start-OSRM-Shaoguan.ps1" -ForegroundColor White
    Write-Host "3. OSRM服务正在端口5000运行" -ForegroundColor White
    exit 1
}

# 测试Table API（批量距离矩阵）
Write-Host "`n🔢 测试Table API..." -ForegroundColor Yellow
try {
    # 韶关市几个关键坐标点
    $coordinates = "114.002334,24.053498;114.343144,23.927291;114.19539,24.05113"
    $tableUrl = "$osrmUrl/table/v1/driving/$coordinates"
    
    $response = Invoke-WebRequest -Uri $tableUrl -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        $jsonResponse = $response.Content | ConvertFrom-Json
        if ($jsonResponse.code -eq "Ok") {
            Write-Host "✅ Table API工作正常" -ForegroundColor Green
            
            $durations = $jsonResponse.durations
            $distances = $jsonResponse.distances
            
            Write-Host "📊 距离矩阵 (3x3):" -ForegroundColor Cyan
            for ($i = 0; $i -lt 3; $i++) {
                $row = ""
                for ($j = 0; $j -lt 3; $j++) {
                    $dist = [math]::Round($distances[$i][$j]/1000, 1)
                    $row += "$dist".PadLeft(6) + "km "
                }
                Write-Host "   $row" -ForegroundColor White
            }
            
            Write-Host "⏱️  时间矩阵 (3x3):" -ForegroundColor Cyan
            for ($i = 0; $i -lt 3; $i++) {
                $row = ""
                for ($j = 0; $j -lt 3; $j++) {
                    $time = [math]::Round($durations[$i][$j]/60, 1)
                    $row += "$time".PadLeft(6) + "min"
                }
                Write-Host "   $row" -ForegroundColor White
            }
        } else {
            Write-Host "⚠️  Table API返回错误: $($jsonResponse.code)" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ Table API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 OSRM服务测试完成！" -ForegroundColor Green
Write-Host "现在可以运行Java代码生成真实路径数据:" -ForegroundColor Yellow
Write-Host "routeTest001.testOSRMDataGeneration()" -ForegroundColor White
