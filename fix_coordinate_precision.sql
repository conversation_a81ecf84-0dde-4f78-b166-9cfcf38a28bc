-- 修复坐标精度不一致问题

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 重新生成时间矩阵，使用与accumulation表一致的坐标精度
-- 中转站1内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站1内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站2内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站2内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站3内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站3内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站4内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站4内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站5内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站5内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 中转站6内部矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as longitude_start,
    CAST(a1.latitude AS CHAR) as latitude_start,
    CAST(a2.longitude AS CHAR) as longitude_end,
    CAST(a2.latitude AS CHAR) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站6内部矩阵完成' as status, COUNT(*) as records FROM travel_time;

-- 最终统计
SELECT 
    '坐标精度修复完成' as status,
    COUNT(*) as total_records
FROM travel_time;

-- 验证坐标格式
SELECT 
    '坐标格式验证' as check_type,
    longitude_start, latitude_start, longitude_end, latitude_end
FROM travel_time 
LIMIT 3;
