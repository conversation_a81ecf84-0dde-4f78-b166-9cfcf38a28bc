-- 简化版精确坐标修复脚本

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 为中转站1生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 3. 为中转站2生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 4. 为中转站3生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 5. 为中转站4生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 6. 为中转站5生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 7. 为中转站6生成数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    FORMAT(a1.longitude, 6) as longitude_start,
    FORMAT(a1.latitude, 6) as latitude_start,
    FORMAT(a2.longitude, 6) as longitude_end,
    FORMAT(a2.latitude, 6) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

-- 8. 显示结果统计
SELECT 
    '简化版修复完成' as status,
    COUNT(*) as total_records,
    ROUND(COUNT(*) / 678460.0 * 100, 2) as coverage_percentage
FROM travel_time;

-- 9. 验证坐标匹配情况
SELECT 
    '坐标匹配验证' as check_type,
    COUNT(DISTINCT CONCAT(longitude_start, ',', latitude_start)) as unique_start_coords,
    COUNT(DISTINCT CONCAT(longitude_end, ',', latitude_end)) as unique_end_coords
FROM travel_time;
