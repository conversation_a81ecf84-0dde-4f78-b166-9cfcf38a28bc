# 使用Docker运行OSRM的简化方案

Write-Host "Setting up OSRM with Docker..." -ForegroundColor Green

# 检查Docker是否安装
Write-Host "Checking Docker installation..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "Docker found: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "Docker not found. Installing Docker Desktop..." -ForegroundColor Yellow
    Write-Host "Please download and install Docker Desktop from:" -ForegroundColor Red
    Write-Host "https://www.docker.com/products/docker-desktop" -ForegroundColor White
    Write-Host "After installation, restart this script." -ForegroundColor Yellow
    Read-Host "Press Enter to continue after Docker installation"
    exit 1
}

# 创建工作目录
$workDir = "osrm-data"
if (!(Test-Path $workDir)) {
    New-Item -ItemType Directory -Path $workDir
    Write-Host "Created directory: $workDir" -ForegroundColor Yellow
}

Set-Location $workDir

# 下载韶关市地图数据
Write-Host "Downloading Shaoguan map data..." -ForegroundColor Yellow
$mapUrl = "http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
$mapFile = "guangdong-latest.osm.pbf"

if (!(Test-Path $mapFile)) {
    Write-Host "Downloading Guangdong map (about 200MB)..." -ForegroundColor Cyan
    Write-Host "This may take several minutes..." -ForegroundColor Yellow
    
    try {
        # 使用PowerShell下载并显示进度
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($mapUrl, "$PWD\$mapFile")
        Write-Host "Map download completed" -ForegroundColor Green
    } catch {
        Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Please manually download: $mapUrl" -ForegroundColor Yellow
        Write-Host "Save as: $PWD\$mapFile" -ForegroundColor Yellow
        Read-Host "Press Enter after download completes"
    }
} else {
    Write-Host "Map file already exists" -ForegroundColor Green
}

# 验证文件存在
if (!(Test-Path $mapFile)) {
    Write-Host "Map file not found. Cannot proceed." -ForegroundColor Red
    exit 1
}

Write-Host "Map file size: $((Get-Item $mapFile).Length / 1MB) MB" -ForegroundColor Cyan

# 使用Docker处理地图数据
Write-Host "Processing map data with Docker..." -ForegroundColor Yellow

# 第1步：提取路网
Write-Host "Step 1: Extracting road network..." -ForegroundColor Cyan
try {
    docker run -t -v "${PWD}:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/$mapFile
    Write-Host "Road network extraction completed" -ForegroundColor Green
} catch {
    Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Trying to pull OSRM image first..." -ForegroundColor Yellow
    docker pull osrm/osrm-backend
    docker run -t -v "${PWD}:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/$mapFile
}

# 第2步：分区
Write-Host "Step 2: Creating partitions..." -ForegroundColor Cyan
$osrmFile = $mapFile -replace '\.osm\.pbf$', '.osrm'
docker run -t -v "${PWD}:/data" osrm/osrm-backend osrm-partition /data/$osrmFile

# 第3步：自定义
Write-Host "Step 3: Customizing..." -ForegroundColor Cyan
docker run -t -v "${PWD}:/data" osrm/osrm-backend osrm-customize /data/$osrmFile

Write-Host "OSRM data processing completed!" -ForegroundColor Green

# 创建启动脚本
Write-Host "Creating startup script..." -ForegroundColor Yellow

$startScript = @"
# Start OSRM service with Docker

Write-Host "Starting OSRM service..." -ForegroundColor Green

Set-Location osrm-data

# Check if processed data exists
if (!(Test-Path "guangdong-latest.osrm")) {
    Write-Host "Processed OSRM data not found!" -ForegroundColor Red
    Write-Host "Please run: .\setup-osrm-docker.ps1 first" -ForegroundColor Yellow
    exit 1
}

Write-Host "Starting OSRM server on port 5000..." -ForegroundColor Yellow
Write-Host "Map data: Guangdong Province (includes Shaoguan)" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow

try {
    docker run -t -i -p 5000:5000 -v "`${PWD}:/data" osrm/osrm-backend osrm-routed --algorithm mld /data/guangdong-latest.osrm
} catch {
    Write-Host "Failed to start OSRM server" -ForegroundColor Red
    Write-Host "Make sure Docker is running" -ForegroundColor Yellow
}
"@

$startScript | Out-File -FilePath "..\start-osrm-docker.ps1" -Encoding UTF8

# 创建测试脚本
$testScript = @"
# Test OSRM service

Write-Host "Testing OSRM service..." -ForegroundColor Green

`$osrmUrl = "http://localhost:5000"

# Test basic route
Write-Host "Testing route service..." -ForegroundColor Yellow
try {
    `$response = Invoke-WebRequest -Uri "`$osrmUrl/route/v1/driving/114.002334,24.053498;114.343144,23.927291?overview=false" -UseBasicParsing -TimeoutSec 10
    if (`$response.StatusCode -eq 200) {
        Write-Host "Route service working!" -ForegroundColor Green
        `$jsonResponse = `$response.Content | ConvertFrom-Json
        if (`$jsonResponse.code -eq "Ok") {
            `$route = `$jsonResponse.routes[0]
            `$distance = `$route.distance
            `$duration = `$route.duration
            
            Write-Host "Test route results:" -ForegroundColor Cyan
            Write-Host "  Distance: `$([math]::Round(`$distance/1000, 2)) km" -ForegroundColor White
            Write-Host "  Duration: `$([math]::Round(`$duration/60, 1)) minutes" -ForegroundColor White
        }
    }
} catch {
    Write-Host "Route service test failed: `$(`$_.Exception.Message)" -ForegroundColor Red
}

# Test table service
Write-Host "Testing table service..." -ForegroundColor Yellow
try {
    `$coordinates = "114.002334,24.053498;114.343144,23.927291;114.19539,24.05113"
    `$tableUrl = "`$osrmUrl/table/v1/driving/`$coordinates"
    
    `$response = Invoke-WebRequest -Uri `$tableUrl -UseBasicParsing -TimeoutSec 10
    if (`$response.StatusCode -eq 200) {
        Write-Host "Table service working!" -ForegroundColor Green
        `$jsonResponse = `$response.Content | ConvertFrom-Json
        if (`$jsonResponse.code -eq "Ok") {
            Write-Host "Distance matrix (3x3) generated successfully" -ForegroundColor Cyan
        }
    }
} catch {
    Write-Host "Table service test failed: `$(`$_.Exception.Message)" -ForegroundColor Red
}

Write-Host "OSRM service test completed!" -ForegroundColor Green
"@

$testScript | Out-File -FilePath "..\test-osrm-docker.ps1" -Encoding UTF8

Set-Location ..

Write-Host "Setup completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run: .\start-osrm-docker.ps1" -ForegroundColor White
Write-Host "2. In another terminal, run: .\test-osrm-docker.ps1" -ForegroundColor White
Write-Host "3. Use the Java RouteTest001 method to generate travel_time data" -ForegroundColor White
