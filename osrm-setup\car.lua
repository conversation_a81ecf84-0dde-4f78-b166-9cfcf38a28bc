﻿api_version = 4

function setup()
  return {
    properties = {
      max_speed_for_map_matching = 180/3.6,
      use_turn_restrictions = true,
      continue_straight_at_waypoint = true,
      weight_name = 'routability',
    },
    default_mode = 1,
    default_speed = 50,
    oneway_handling = true,
    turn_penalty = 7.5,
    speeds = {
      motorway = 120,
      motorway_link = 80,
      trunk = 100,
      trunk_link = 70,
      primary = 80,
      primary_link = 60,
      secondary = 60,
      secondary_link = 50,
      tertiary = 50,
      tertiary_link = 40,
      unclassified = 40,
      residential = 30,
      living_street = 20,
      service = 20
    }
  }
end

function process_node(profile, node, result)
  local barrier = node:get_value_by_key("barrier")
  if barrier then
    result.barrier = true
  end
  
  local tag = node:get_value_by_key("highway")
  if "traffic_signals" == tag then
    result.traffic_lights = true
  end
end

function process_way(profile, way, result)
  local data = {
    highway = way:get_value_by_key('highway'),
  }

  if (not data.highway or data.highway == '') then
    return
  end

  if data.highway == 'construction' then
    return
  end

  result.forward_mode = 1
  result.backward_mode = 1

  local highway_speed = profile.default_speed
  if profile.speeds[data.highway] then
    highway_speed = profile.speeds[data.highway]
  end

  result.forward_speed = highway_speed
  result.backward_speed = highway_speed

  local name = way:get_value_by_key('name')
  if name and "" ~= name then
    result.name = name
  else
    result.name = ""
  end

  local oneway = way:get_value_by_key('oneway')
  if oneway == "-1" then
    result.forward_mode = 0
  elseif oneway == "yes" or oneway == "1" or oneway == "true" then
    result.backward_mode = 0
  end

  result.weight = result.distance / result.forward_speed
end

function process_turn(profile, turn)
  if turn.has_traffic_light then
    turn.weight = turn.weight + 2
  end
  
  if turn.angle >= 0 then
    turn.weight = turn.weight + profile.turn_penalty / (1 + math.exp(-((13) * turn.angle/180 - 6.5)))
  else
    turn.weight = turn.weight + profile.turn_penalty / (1 + math.exp(-((13) * -turn.angle/180 - 6.5)))
  end
end

return {
  setup = setup,
  process_way = process_way,
  process_node = process_node,
  process_turn = process_turn
}
