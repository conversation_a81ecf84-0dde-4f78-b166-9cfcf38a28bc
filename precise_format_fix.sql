-- 精确匹配算法期望的坐标格式

-- 1. 清空travel_time表
TRUNCATE TABLE travel_time;

-- 2. 创建一个函数来模拟Python的_remove_trailing_zeros逻辑
-- 由于MySQL没有直接的函数，我们使用TRIM和REPLACE来处理

-- 3. 生成中转站1的完整时间矩阵（精确格式匹配）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    -- 模拟_remove_trailing_zeros的逻辑：去除尾随零
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 1 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 1 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站1精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 4. 生成中转站2的完整时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 2 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 2 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站2精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 5. 生成中转站3的完整时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 3 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 3 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站3精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 6. 生成中转站4的完整时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 4 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 4 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站4精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 7. 生成中转站5的完整时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 5 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 5 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站5精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 8. 生成中转站6的完整时间矩阵
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.longitude AS CHAR))) as longitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a1.latitude AS CHAR))) as latitude_start,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.longitude AS CHAR))) as longitude_end,
    TRIM(TRAILING '0' FROM TRIM(TRAILING '.' FROM CAST(a2.latitude AS CHAR))) as latitude_end,
    GREATEST(1.0, 
        (6371 * 2 * ASIN(SQRT(
            POWER(SIN((RADIANS(a2.latitude) - RADIANS(a1.latitude)) / 2), 2) +
            COS(RADIANS(a1.latitude)) * COS(RADIANS(a2.latitude)) * 
            POWER(SIN((RADIANS(a2.longitude) - RADIANS(a1.longitude)) / 2), 2)
        )) / 40.0 * 60.0) + RAND() * 5.0
    ) as estimated_time
FROM (
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a1,
(
    SELECT longitude, latitude FROM accumulation WHERE transit_depot_id = 6 AND is_delete = 0 AND longitude != 0 AND latitude != 0
    UNION ALL
    SELECT CAST(longitude AS DECIMAL(10,6)), CAST(latitude AS DECIMAL(10,6)) FROM transit_depot WHERE transit_depot_id = 6 AND is_delete = 0
) a2
WHERE NOT (a1.longitude = a2.longitude AND a1.latitude = a2.latitude);

SELECT '中转站6精确格式完成' as status, COUNT(*) as records FROM travel_time;

-- 9. 最终统计和验证
SELECT 
    '精确格式修复完成' as status,
    COUNT(*) as total_records
FROM travel_time;

-- 10. 验证坐标格式（应该与Python的_remove_trailing_zeros输出一致）
SELECT 
    '格式验证' as check_type,
    longitude_start, latitude_start, longitude_end, latitude_end
FROM travel_time 
LIMIT 5;
